import * as fs from 'fs';
import * as path from 'path';
// import { v4 as uuidv4 } from 'uuid'; // 暂时注释，使用简单的ID生成
import Store from 'electron-store';
import { FFmpegService } from './ffmpeg-service';
import { ASRService } from './asr-service';
import { convertLocalFile, downloadAndConvert, downloadFile } from '../utils/img-utils';

export interface ConvertOptions {
  outputFormat: string;
  quality?: string;
  maxWidth?: number;
  resizeEnabled?: boolean;
}

export interface ASROptions {
  language: string;
  outputFormats: string[];
  outputDirectory?: string;
  outputPaths?: string[];
}

export interface MediaTask {
  id: string;
  fileName: string;
  filePath: string;
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process';
  options: ConvertOptions | ASROptions;
  outputPath: string;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'paused';
  progress: number;
  error?: string;
  startTime?: number;
  endTime?: number;
}


export class MediaService {
  private store: Store;
  private ffmpegService: FFmpegService;
  private asrService: ASRService;
  private activeTasks: Set<string> = new Set();
  private tempDir: string;
  private taskProgressCallbacks: Map<string, (progress: number) => void> = new Map();
  private singleTasks: Map<string, MediaTask> = new Map(); // 单个任务管理

  constructor() {
    this.store = new Store();
    this.ffmpegService = new FFmpegService();
    this.asrService = new ASRService();
    this.tempDir = path.join(__dirname, '../../temp/media');

    // 确保临时目录存在
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }

    // 启动时清理旧的临时文件
    this.cleanupTempFiles();
  }

  /**
   * 视频转换功能
   */
  async convertVideo(inputPath: string, outputPath: string, options: ConvertOptions, taskId?: string): Promise<string> {
    console.log(`[MediaService] 开始转换视频: ${inputPath} -> ${outputPath}`);

    if (!fs.existsSync(inputPath)) {
      throw new Error(`输入文件不存在: ${inputPath}`);
    }

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const startTime = Date.now();

    try {
      // 如果有taskId，注册进度回调
      if (taskId) {
        this.ffmpegService.registerProgressCallback(taskId, (progress) => {
          this.updateTaskProgress(taskId, progress.percent || 0);
        });
      }

      const result = await this.ffmpegService.convertVideo(inputPath, outputPath, options, taskId);

      const processingTime = Date.now() - startTime;

      // 清理进度回调
      if (taskId) {
        this.ffmpegService.removeProgressCallback(taskId);
      }

      return result;
    } catch (error) {
      console.error(`[MediaService] 视频转换失败:`, error);

      // 清理进度回调
      if (taskId) {
        this.ffmpegService.removeProgressCallback(taskId);
      }

      throw error;
    }
  }

  /**
   * 从视频提取音频
   */
  async extractAudio(videoPath: string, audioPath: string, options?: { quality?: string }, taskId?: string): Promise<string> {
    console.log(`[MediaService] 开始提取音频: ${videoPath} -> ${audioPath}`);

    if (!fs.existsSync(videoPath)) {
      throw new Error(`视频文件不存在: ${videoPath}`);
    }

    // 确保输出目录存在
    const outputDir = path.dirname(audioPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const startTime = Date.now();

    try {
      // 如果有taskId，注册进度回调
      if (taskId) {
        this.ffmpegService.registerProgressCallback(taskId, (progress) => {
          this.updateTaskProgress(taskId, progress.percent || 0);
        });
      }

      const result = await this.ffmpegService.extractAudio(videoPath, audioPath, options, taskId);

      const processingTime = Date.now() - startTime;

      // 清理进度回调
      if (taskId) {
        this.ffmpegService.removeProgressCallback(taskId);
      }

      return result;
    } catch (error) {
      console.error(`[MediaService] 音频提取失败:`, error);

      // 清理进度回调
      if (taskId) {
        this.ffmpegService.removeProgressCallback(taskId);
      }

      throw error;
    }
  }

  /**
   * 语音识别提取文字（支持音频和视频文件）
   */
  async extractTextFromAudio(filePath: string, options: ASROptions, taskId?: string): Promise<{ text: string; srt: string }> {
    console.log(`[MediaService] 开始语音识别: ${filePath}`);

    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    const startTime = Date.now();

    try {
      // 检查文件类型，决定使用哪种处理方式
      const fileExtension = path.extname(filePath).toLowerCase();
      const videoFormats = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'];
      const audioFormats = ['.mp3', '.wav', '.m4a', '.aac', '.flac'];

      let result;

      if (videoFormats.includes(fileExtension)) {
        console.log(`[MediaService] 检测到视频文件，使用视频处理方式: ${filePath}`);
        result = await this.asrService.extractTextFromVideo(filePath, options);
      } else if (audioFormats.includes(fileExtension)) {
        console.log(`[MediaService] 检测到音频文件，使用音频处理方式: ${filePath}`);
        result = await this.asrService.extractText(filePath, options);
      } else {
        throw new Error(`不支持的文件格式: ${fileExtension}`);
      }

      const processingTime = Date.now() - startTime;
      console.log(`[MediaService] 语音识别完成，耗时: ${processingTime}ms`);

      return result;
    } catch (error) {
      console.error(`[MediaService] 语音识别失败:`, error);
      throw error;
    }
  }

  /**
   * 批量图片处理（复用现有功能）
   */
  async processImages(imagePaths: string[], options: ConvertOptions, taskId?: string): Promise<string[]> {
    console.log(`[MediaService] 开始批量处理图片: ${imagePaths.length}个文件`);

    const startTime = Date.now();
    const results: string[] = [];
    let totalSize = 0;

    for (const imagePath of imagePaths) {
      if (!fs.existsSync(imagePath)) {
        console.warn(`[MediaService] 图片文件不存在，跳过: ${imagePath}`);
        continue;
      }

      // 检查是否为目录，如果是则跳过
      const stats = fs.statSync(imagePath);
      if (stats.isDirectory()) {
        console.warn(`[MediaService] 路径是一个目录，不是图片文件，跳过: ${imagePath}`);
        continue;
      }

      // 累计文件大小用于统计
      totalSize += stats.size;

      try {
        const fileName = path.basename(imagePath, path.extname(imagePath));
        const originalExt = path.extname(imagePath).slice(1);
        const outputFormat = options.outputFormat === 'original' ? originalExt : (options.outputFormat || originalExt);
        const outputDir = path.dirname(imagePath);
        const outputPath = path.join(outputDir, `${fileName}_processed.${outputFormat}`);

        // 处理质量参数
        let quality = 85; // 默认值
        if (options.quality && !isNaN(parseInt(options.quality))) {
          quality = parseInt(options.quality);
        }

        // 处理宽度调整参数
        let maxWidth: number | undefined = undefined;
        if (options.resizeEnabled && options.maxWidth && options.maxWidth > 0) {
          maxWidth = options.maxWidth;
          console.log(`[MediaService] 将调整图片大小，最大宽度: ${maxWidth}px`);
        }

        if (options.outputFormat && options.outputFormat !== 'original') {
          // 转换格式并可能应用质量设置和尺寸调整
          await convertLocalFile(
            imagePath,
            outputDir,
            outputFormat,
            quality,
            maxWidth
          );
        } else if (options.quality || maxWidth) {
          // 只压缩或调整尺寸，不转换格式
          await convertLocalFile(
            imagePath,
            outputDir,
            originalExt,
            quality,
            maxWidth
          );
        } else {
          // 没有转换格式、压缩和尺寸调整需求，直接复制文件
          fs.copyFileSync(imagePath, outputPath);
        }

        results.push(outputPath);
      } catch (error) {
        console.error(`[MediaService] 处理图片失败: ${imagePath}`, error);
        throw error;
      }
    }

    // 更新统计信息
    const processingTime = Date.now() - startTime;

    return results;
  }



  /**
   * 设置任务进度回调处理器
   */
  onTaskProgress: ((taskId: string, progress: number) => void) | undefined;

  /**
   * 设置任务状态回调处理器
   */
  onTaskStatus: ((taskId: string, status: string, error?: string) => void) | undefined;

  /**
   * 设置任务完成回调处理器
   */
  onTaskCompleted: ((taskId: string, result: any) => void) | undefined;

  /**
   * 更新任务进度
   */
  private updateTaskProgress(taskId: string, progress: number): void {
    // 调用全局进度回调
    if (this.onTaskProgress) {
      this.onTaskProgress(taskId, progress);
    }


    // 检查单个任务
    const singleTask = this.singleTasks.get(taskId);
    if (singleTask) {
      singleTask.progress = progress;
    }

    // 定期持久化进度（每隔10%）
    if (progress % 10 === 0 || progress === 100) {
      this.updateTaskProgressWithPersistence(taskId, progress).catch(error => {
        console.error(`[MediaService] 持久化任务进度失败: ${taskId}`, error);
      });
    }
  }

  /**
   * 更新任务进度并持久化单个任务
   * @param taskId 任务ID
   * @param progress 进度值（0-100）
   */
  private async updateTaskProgressWithPersistence(taskId: string, progress: number): Promise<void> {
    // 调用全局进度回调（不调用updateTaskProgress避免递归）
    if (this.onTaskProgress) {
      this.onTaskProgress(taskId, progress);
    }

    // 检查单个任务并更新进度
    const singleTask = this.singleTasks.get(taskId);
    if (singleTask) {
      singleTask.progress = progress;

      try {
        const Store = (await import('electron-store')).default;
        const store = new Store();

        const savedTasks = store.get('singleTasks', {}) as Record<string, any>;
        savedTasks[taskId] = singleTask;
        store.set('singleTasks', savedTasks);

        console.log(`[MediaService] 单个任务进度已持久化: ${taskId} (${progress}%)`);
      } catch (error) {
        console.error(`[MediaService] 持久化任务进度失败: ${taskId}`, error);
      }
    }
  }

  /**
   * 暂停任务并返回是否成功
   * @param taskId 任务ID
   * @returns 是否成功暂停
   */
  async pauseTask(taskId: string): Promise<boolean> {
    const task = this.findTaskById(taskId);
    if (task && task.status === 'processing') {
      this.ffmpegService.pauseTask(taskId);
      task.status = 'paused'; // 修正：使用正确的'paused'状态
      console.log(`[MediaService] 任务已暂停: ${taskId}`);

      // 持久化任务状态
      await this.persistTaskState(task);
      return true;
    }
    return false;
  }

  /**
   * 恢复任务并返回是否成功
   * @param taskId 任务ID
   * @returns 是否成功恢复
   */
  async resumeTask(taskId: string): Promise<boolean> {
    const task = this.findTaskById(taskId);
    if (task && task.status === 'paused') {
      this.ffmpegService.resumeTask(taskId);
      task.status = 'processing';
      console.log(`[MediaService] 任务已恢复: ${taskId}`);

      // 持久化任务状态
      await this.persistTaskState(task);
      return true;
    }
    return false;
  }

  /**
   * 取消任务并返回是否成功
   * @param taskId 任务ID
   * @returns 是否成功取消
   */
  async cancelTask(taskId: string): Promise<boolean> {
    const task = this.findTaskById(taskId);
    if (task) {
      this.ffmpegService.cancelTask(taskId);
      task.status = 'error';
      task.error = '任务已取消';
      console.log(`[MediaService] 任务已取消: ${taskId}`);

      // 持久化任务状态
      await this.persistTaskState(task);
      return true;
    }
    return false;
  }

  /**
   * 持久化任务状态
   * @param task 任务对象
   */
  private async persistTaskState(task: MediaTask): Promise<void> {
    try {
      const Store = (await import('electron-store')).default;
      const store = new Store();

      // 如果是单个任务
      const savedTasks = store.get('singleTasks', {}) as Record<string, any>;
      savedTasks[task.id] = task;
      store.set('singleTasks', savedTasks);
      console.log(`[MediaService] 单个任务状态已持久化: ${task.id}`);
    } catch (error) {
      console.error(`[MediaService] 持久化任务状态失败: ${task.id}`, error);
    }
  }


  /**
   * 根据ID查找任务
   */
  private findTaskById(taskId: string): MediaTask | undefined {
    // 在单个任务中查找
    return this.singleTasks.get(taskId);
  }

  /**
   * FFmpeg可用性检查
   */
  async checkFFmpegStatus(): Promise<{
    available: boolean;
    version?: string;
    path?: string;
    error?: string;
  }> {
    try {
      const available = await this.ffmpegService.checkFFmpegAvailable();
      if (available) {
        const version = await this.ffmpegService.getVersion();
        return {
          available: true,
          version,
          path: this.ffmpegService.getFFmpegPath()
        };
      } else {
        return {
          available: false,
          error: 'FFmpeg not available'
        };
      }
    } catch (error) {
      return {
        available: false,
        error: error instanceof Error ? error.message : '检查失败'
      };
    }
  }






  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<MediaTask> {
    const task = this.singleTasks.get(taskId);
    if (task) {
      return { ...task }; // 返回副本
    }

    throw new Error(`任务不存在: ${taskId}`);
  }

  /**
   * 获取支持的格式
   */
  getSupportedFormats(): { video: string[]; audio: string[]; image: string[] } {
    return {
      video: ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'],
      audio: ['mp3', 'wav', 'aac', 'm4a', 'flac', 'ogg'],
      image: ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'gif']
    };
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath: string): Promise<{
    duration?: number;
    size: number;
    format: string;
    dimensions?: { width: number; height: number };
  }> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).slice(1).toLowerCase();

    const fileInfo = {
      size: stats.size,
      format: ext
    };

    // 如果是视频或音频文件，获取时长信息
    if (['mp4', 'avi', 'mov', 'mkv', 'flv', 'mp3', 'wav', 'aac'].includes(ext)) {
      try {
        const mediaInfo = await this.ffmpegService.getMediaInfo(filePath);
        return { ...fileInfo, ...mediaInfo };
      } catch (error) {
        console.warn(`[MediaService] 无法获取媒体文件信息: ${filePath}`, error);
      }
    }

    return fileInfo;
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<void> {
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        for (const file of files) {
          const filePath = path.join(this.tempDir, file);
          const stats = fs.statSync(filePath);

          // 删除超过24小时的临时文件
          if (Date.now() - stats.mtime.getTime() > 24 * 60 * 60 * 1000) {
            fs.unlinkSync(filePath);
            console.log(`[MediaService] 清理临时文件: ${filePath}`);
          }
        }
      }
    } catch (error) {
      console.error(`[MediaService] 清理临时文件失败:`, error);
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 执行单个媒体任务
   * @param task 任务对象
   * @returns 处理结果
   */
  async executeMediaTask(task: MediaTask): Promise<any> {
    console.log(`[MediaService] 开始执行任务: ${task.id}, 类型: ${task.type}`);

    // 更新任务状态和开始时间
    task.status = 'processing';
    task.startTime = Date.now();
    task.progress = 0;

    // 持久化任务状态
    await this.persistTaskState(task);

    // 通知状态变更
    if (this.onTaskStatus) {
      this.onTaskStatus(task.id, task.status);
    }

    try {
      let result;

      switch (task.type) {
        case 'video-convert':
          result = await this.convertVideo(task.filePath, task.outputPath, task.options as ConvertOptions, task.id);
          break;

        case 'audio-extract':
          result = await this.extractAudio(task.filePath, task.outputPath, task.options as any, task.id);
          break;

        case 'asr':
          result = await this.extractTextFromAudio(task.filePath, task.options as ASROptions, task.id);
          break;

        case 'image-process':
          result = await this.processImages([task.filePath], task.options as ConvertOptions, task.id);
          break;

        default:
          throw new Error(`不支持的任务类型: ${task.type}`);
      }

      // 更新任务状态为完成
      task.status = 'completed';
      task.progress = 100;
      task.endTime = Date.now();

      // 计算处理时间（毫秒）
      const processingTime = task.endTime - task.startTime;

      // 持久化任务状态
      await this.persistTaskState(task);

      // 通知状态变更
      if (this.onTaskStatus) {
        this.onTaskStatus(task.id, task.status);
      }

      // 通知任务完成
      if (this.onTaskCompleted) {
        this.onTaskCompleted(task.id, {
          ...result,
          processingTime,
          outputPath: task.outputPath
        });
      }

      console.log(`[MediaService] 任务完成: ${task.id}, 耗时: ${processingTime}ms`);
      return result;
    } catch (error) {
      // 更新任务状态为错误
      task.status = 'error';
      task.endTime = Date.now();
      task.error = error instanceof Error ? error.message : '未知错误';

      // 持久化任务状态
      await this.persistTaskState(task);

      // 通知状态变更
      if (this.onTaskStatus) {
        this.onTaskStatus(task.id, task.status, task.error);
      }

      console.error(`[MediaService] 任务失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 启动单个任务
   * @param taskId 任务ID
   * @returns 处理结果
   */
  async startTask(taskId: string): Promise<any> {
    const task = this.singleTasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }

    // 检查任务状态
    if (task.status === 'processing') {
      console.warn(`[MediaService] 任务已在处理中: ${taskId}`);
      return;
    }

    if (task.status === 'completed') {
      console.warn(`[MediaService] 任务已完成，不需要再次处理: ${taskId}`);
      return;
    }

    return this.executeMediaTask(task);
  }
}